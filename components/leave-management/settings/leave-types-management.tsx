"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { Plus, Settings, Loader2, Edit, Trash2 } from "lucide-react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"

interface LeaveType {
  _id: string
  name: string
  code: string
  description?: string
  defaultDays: number
  isPaid: boolean
  isActive: boolean
  requiresApproval: boolean
  maxConsecutiveDays: number
  minNoticeInDays: number
  allowCarryOver: boolean
  maxCarryOverDays: number
  color: string
}

interface LeaveTypesManagementProps {
  userId: string
}

export function LeaveTypesManagement({ userId }: LeaveTypesManagementProps) {
  const [leaveTypes, setLeaveTypes] = useState<LeaveType[]>([])
  const [loading, setLoading] = useState(true)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const { toast } = useToast()

  // Load leave types
  useEffect(() => {
    const loadLeaveTypes = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/leave/types')
        
        if (!response.ok) {
          throw new Error('Failed to load leave types')
        }

        const result = await response.json()
        setLeaveTypes(result.data || [])
      } catch (error) {
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to load leave types",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    loadLeaveTypes()
  }, [toast])

  const handleSeedLeaveTypes = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/seed/leave-types', {
        method: 'POST',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to seed leave types')
      }

      const result = await response.json()
      toast({
        title: "Success",
        description: result.message || "Leave types seeded successfully",
      })

      // Reload leave types
      const loadResponse = await fetch('/api/leave/types')
      if (loadResponse.ok) {
        const loadResult = await loadResponse.json()
        setLeaveTypes(loadResult.data || [])
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to seed leave types",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Leave Types Management
            </CardTitle>
            <CardDescription>
              Configure and manage different types of leave available to employees
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={handleSeedLeaveTypes}
              disabled={loading}
            >
              {loading ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Plus className="h-4 w-4 mr-2" />
              )}
              Seed Default Types
            </Button>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Leave Type
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add New Leave Type</DialogTitle>
                  <DialogDescription>
                    Create a new leave type with specific rules and entitlements.
                  </DialogDescription>
                </DialogHeader>
                <div className="p-4">
                  <p className="text-sm text-muted-foreground">
                    Leave type creation form will be implemented here.
                  </p>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading leave types...</span>
          </div>
        ) : leaveTypes.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground mb-4">
              No leave types configured yet.
            </p>
            <Button onClick={handleSeedLeaveTypes} disabled={loading}>
              <Plus className="h-4 w-4 mr-2" />
              Seed Default Leave Types
            </Button>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Code</TableHead>
                <TableHead>Default Days</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {leaveTypes.map((leaveType) => (
                <TableRow key={leaveType._id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <div 
                        className="w-3 h-3 rounded-full" 
                        style={{ backgroundColor: leaveType.color }}
                      />
                      <div>
                        <div className="font-medium">{leaveType.name}</div>
                        {leaveType.description && (
                          <div className="text-sm text-muted-foreground">
                            {leaveType.description}
                          </div>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{leaveType.code}</Badge>
                  </TableCell>
                  <TableCell>{leaveType.defaultDays}</TableCell>
                  <TableCell>
                    <Badge variant={leaveType.isPaid ? "default" : "secondary"}>
                      {leaveType.isPaid ? "Paid" : "Unpaid"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={leaveType.isActive ? "default" : "secondary"}>
                      {leaveType.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  )
}
